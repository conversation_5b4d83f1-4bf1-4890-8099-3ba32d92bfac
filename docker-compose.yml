services:
    dcmtrd-app:
        build:
            context: .
            dockerfile: docker/DockerFile
        image: matkmin/docker-image-2024:latest
        container_name: "dcmtrd-app"
        platform: linux/amd64
        networks:
            - app-network
        restart: unless-stopped
        extra_hosts:
            - "host.docker.internal:host-gateway"
        ports:
            - 5176:5176
        volumes:
            - ".:/var/www/html"
            - "./docker/php.ini:/usr/local/etc/php/conf.d/php.ini"
    nginx:
        image: matkmin/docker-image-2024:nginx
        container_name: dcmtrd-nginx
        platform: linux/amd64
        restart: unless-stopped
        ports:
            - 8003:80
        working_dir: "/var/www/html"
        volumes:
            - ".:/var/www/html"
            - "./docker/nginx.conf:/etc/nginx/conf.d/default.conf"
        networks:
            - app-network
        depends_on:
            - dcmtrd-app
    mariadb:
        image: matkmin/docker-image-2024:mariadb
        container_name: dcmtrd-mariadb
        platform: linux/amd64
        restart: unless-stopped
        environment:
            MARIADB_ROOT_PASSWORD: "${DB_PASSWORD}"
            MARIADB_DATABASE: "${DB_DATABASE}"
            MARIADB_USER: "${DB_USERNAME}"
            MARIADB_PASSWORD: "${DB_PASSWORD}"
            MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: "yes"
        ports:
            - 3309:3306
        volumes:
            - "vol-dcmtrd-mariadb:/var/lib/mysql"
        networks:
            - app-network
    redis:
        image: matkmin/docker-image-2024:redisdb
        container_name: dcmtrd-redis
        platform: linux/amd64
        restart: unless-stopped
        environment:
            - ALLOW_EMPTY_PASSWORD=yes
        ports:
            - "6385:6379"
        networks:
            - app-network
    mailhog:
        image: matkmin/docker-image-2024:mailhog
        container_name: dcmtrd-mailhog
        platform: linux/amd64
        ports:
            - 1028:1025
            - 8028:8025
        networks:
            - app-network

networks:
    app-network:
        driver: bridge
volumes:
    vol-dcmtrd-mariadb:
        driver: local
