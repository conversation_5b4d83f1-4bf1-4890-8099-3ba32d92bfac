@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Button */
.btn-primary {
  @apply px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors;
}

.btn-secondary {
  @apply px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors;
}

.hover\:rotate-180:hover {
  transform: rotate(180deg);
}
.transition-transform {
  transition: transform 150ms ease-in-out;
}