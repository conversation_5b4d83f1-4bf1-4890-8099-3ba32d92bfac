<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                {{ __('Maker Dashboard') }}
            </h2>

            <!-- Dropdown Menu - Only visible for DCMTRD section -->
            <div class="relative" x-data="{ open: false }" id="header-dropdown" style="display: none;">
                <button @click="open = !open"
                    class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none">
                    <span>{{ __('Menu') }}</span>
                    <svg class="ml-2 -mr-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </button>

                <div x-show="open" @click.away="open = false"
                    class="absolute right-0 z-10 w-48 mt-2 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
                    x-transition:enter="transition ease-out duration-200"
                    x-transition:enter-start="transform opacity-0 scale-95"
                    x-transition:enter-end="transform opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-75"
                    x-transition:leave-start="transform opacity-100 scale-100"
                    x-transition:leave-end="transform opacity-0 scale-95">
                    <div class="py-1">
                        <!-- Dashboard -->
                        <a href="{{ route('maker.dashboard', ['section' => 'dcmtrd']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            {{ __('Dashboard') }}
                        </a>

                        <!-- Trustee Fee -->
                        <a href="{{ route('trustee-fee-m.index') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            {{ __('Trustee Fee') }}
                        </a>

                        <!-- Compliance Covenant -->
                        <a href="{{ route('compliance-covenant-m.index') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            {{ __('Compliance Covenant') }}
                        </a>

                        <!-- Activity Diary -->
                        <a href="{{ route('activity-diary-m.index') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            {{ __('Activity Diary') }}
                        </a>

                        <!-- Listing Security -->
                        <a href="{{ route('list-security-m.index') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            {{ __('Listing Security') }}
                        </a>

                        <!-- Audit Log -->
                        <a href="#" class="hidden block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            {{ __('Audit Log') }}
                        </a>

                        <!-- Reports -->
                        <a href="{{ route('dcmt-reports.index') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            {{ __('Reports') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </x-slot>

    <script>
        function confirmApproval(event, issuerName) {
            event.preventDefault();
            if (confirm(`Are you confirm to submit the "${issuerName}" for approval?`)) {
                // If confirmed, proceed to the approval page
                window.location.href = event.currentTarget.href;
            }
        }

        // Check if DCMTRD section is active and show dropdown if it is
        document.addEventListener('DOMContentLoaded', function() {
            // Get the section parameter from the URL
            const urlParams = new URLSearchParams(window.location.search);
            const section = urlParams.get('section') || '';

            // Get the dropdown menu element
            const headerDropdown = document.getElementById('header-dropdown');

            // Show the dropdown only if the section is 'dcmtrd'
            if (section === 'dcmtrd') {
                headerDropdown.style.display = 'block';
            } else {
                headerDropdown.style.display = 'none';
            }
        });
    </script>

    @if (Auth::user()->hasPermission('DCMTRD'))
        <div class="hidden py-12 dashboard-section" id="dcmtrd-section" data-section="dcmtrd">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                @if (session('success'))
                    <div class="p-4 mb-6 border-l-4 border-green-400 bg-green-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="py-6">
                    <h2 class="text-xl font-bold leading-tight text-gray-800">
                        {{ __('Debt Capital Market Trust Unit (DCMT)') }}
                    </h2>
                </div>

                <!-- Cards -->
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <!-- Trustee Fees -->
                    <x-dashboard-card title="Trustee Fees" icon="receipt-refund" :count="$trusteeFeesCount ?? 0" :href="route('trustee-fee-m.index')"
                        color="bg-blue-100" />

                    <!-- Compliance Covenants -->
                    <x-dashboard-card title="Compliance Covenants" icon="document-check" :count="$complianceCovenantCount ?? 0"
                        :href="route('compliance-covenant-m.index')" color="bg-blue-100" />

                    <!-- Activity Dairy -->
                    <x-dashboard-card title="Activity Diary" icon="calendar" :count="$activityDairyCount ?? 0" :href="route('activity-diary-m.index')"
                        color="bg-blue-100" />

                    <!-- Reports -->
                    <x-dashboard-card title="Reports" icon="document" :count="$reportsCount ?? 2" :href="route('dcmt-reports.index')"
                        color="bg-blue-100" />

                    <!-- Corporate Bond Listing Security -->
                    <x-dashboard-card title="Corporate Bond Listing Security" icon="shield-check" :count="$listSecuritiesCount ?? 0"
                        :href="route('list-security-m.index')" color="bg-blue-100" />

                    <!-- Corporate Bond Listing Security -->
                    <x-dashboard-card title="Placement & Fund Transfer" icon="shield-check" :count="$placementFundTransfersCount ?? 0"
                        :href="route('fund-transfer-m.index')" color="bg-blue-100" />
                </div>

                <!-- Issuers -->
                <div class="mt-6 overflow-hidden bg-white rounded-lg shadow">
                    <div class="flex items-center justify-between px-4 py-5 sm:px-6">
                        <h3 class="text-lg font-medium text-gray-900">List of Issuers</h3>
                        <div class="flex gap-2">
                            <a href="{{ route('issuer-m.create') }}"
                                class="inline-flex items-center px-4 py-2 font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 4v16m8-8H4" />
                                </svg>
                                Create New Issuer
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filter Bar -->
                    <div class="px-4 py-4 border-t border-gray-200 bg-gray-50 sm:px-6">
                        <form method="GET">
                            <!-- Fixed section parameter -->
                            <input type="hidden" name="section" value="dcmtrd">

                            <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
                                <!-- Issuer Name Search Field -->
                                <div>
                                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                                    <input type="text" name="search" id="search"
                                        value="{{ old('search', request('search')) }}"
                                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        placeholder="Issuer name, short name, or reg. no...">
                                </div>

                                <!-- Status Filter -->
                                <div>
                                    <label for="status"
                                        class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" id="status"
                                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>
                                            Active</option>
                                        <option value="pending"
                                            {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="rejected"
                                            {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        <option value="inactive"
                                            {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>

                                <!-- Filter Button -->
                                <div class="flex items-end">
                                    <button type="submit"
                                        class="inline-flex items-center px-4 py-2 font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                        </svg>
                                        Search
                                    </button>

                                    @if (request('search') || request('status'))
                                        <a href="{{ route('maker.dashboard', ['section' => 'dcmtrd']) }}"
                                            class="inline-flex items-center px-4 py-2 ml-2 font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                                            Clear
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Issuer Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        No
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Issuer Name
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Registration Number
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Status
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse ($issuers as $issuer)
                                    <tr class="transition-colors hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            {{ $loop->iteration + ($issuers->currentPage() - 1) * $issuers->perPage() }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            <a href="{{ route('bond-m.details', $issuer) }}"
                                                class="text-blue-600 cursor-pointer hover:text-blue-900">
                                                {{ $issuer->issuer_name }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                            {{ $issuer->registration_number }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                            <span
                                                class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            {{ $issuer->status == 'Active'
                                                ? 'bg-green-100 text-green-800'
                                                : ($issuer->status == 'Pending'
                                                    ? 'bg-yellow-100 text-yellow-800'
                                                    : ($issuer->status == 'Rejected'
                                                        ? 'bg-red-100 text-red-800'
                                                        : 'bg-gray-100 text-gray-800')) }}">
                                                {{ $issuer->status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                                            <div class="flex justify-end space-x-2">
                                                @if ($issuer->status == 'Draft' or $issuer->status == 'Rejected')
                                                    <a href="{{ route('issuer-m.approval', $issuer) }}"
                                                        class="text-indigo-600 hover:text-indigo-900"
                                                        title="Submit for Approval"
                                                        onclick="confirmApproval(event, '{{ $issuer->issuer_name }}')">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M14 3v4a1 1 0 001 1h4" />
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M12 16v-5m0 0l-2 2m2-2l2 2" />
                                                        </svg>
                                                    </a>
                                                @endif
                                                <a href="{{ route('issuer-m.show', $issuer) }}"
                                                    class="text-indigo-600 hover:text-indigo-900" title="View">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                    </svg>
                                                </a>
                                                <a href="{{ route('issuer-m.edit', $issuer) }}"
                                                    class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                    </svg>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4"
                                            class="px-6 py-4 text-sm text-center text-gray-500 whitespace-nowrap">
                                            No issuers found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if ($issuers->hasPages())
                        <div class="px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                            {{ $issuers->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    @if (Auth::user()->hasPermission('REITS'))
        <div class="hidden py-12 dashboard-section" id="reits-section" data-section="reits">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                @if (session('success'))
                    <div class="p-4 mb-6 border-l-4 border-green-400 bg-green-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                            </div>
                        </div>
                    </div>
                @endif
                <div class="pb-6">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold leading-tight text-gray-800">
                            {{ __('Real Estate Investment Trusts (REITs)') }}
                        </h2>

                        <a href="{{ route('maker.notification.index') }}">
                            <button type="button"
                                class="relative p-2 text-gray-600 transition-colors duration-200 rounded-full hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9">
                                    </path>
                                </svg>
                                <span
                                    class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                                    {{ $totalNotifications }}
                                </span>
                            </button>
                        </a>
                    </div>
                </div>

                <!-- Cards -->
                <div class="grid grid-cols-1 gap-6 mb-6 md:grid-cols-3 lg:grid-cols-3">
                    <!-- Properties -->
                    <x-dashboard-card title="Properties" icon="office-building" :count="$propertiesCount ?? 0" href="#"
                        color="bg-blue-100" />

                    <!-- Financials -->
                    <x-dashboard-card title="Financials" icon="document-text" :count="$financialsCount ?? 0" href="#"
                        color="bg-blue-100" />

                    <!-- Tenants -->
                    <x-dashboard-card title="Tenants" icon="users" :count="$tenantsCount ?? 0" href="#"
                        color="bg-blue-100" />

                    <!-- Appointment -->
                    <x-dashboard-card title="Appointment" icon="users" :count="$appointmentsCount ?? 0" :href="route('appointment-m.index')"
                        color="bg-blue-100" />

                    <!-- Approval Property -->
                    <x-dashboard-card title="Approval Property" icon="users" :count="$approvalPropertiesCount ?? 0" :href="route('approval-property-m.index')"
                        color="bg-blue-100" />

                    <!-- Site Visit Logs Form -->
                    <x-dashboard-card title="Activity Diary" icon="users" :count="$siteVisitLogsCount ?? 0" :href="route('site-visit-log-m.index')"
                        color="bg-blue-100" />
                </div>

                <!-- Table Portfolio -->
                <div class="mt-6 overflow-hidden bg-white rounded-lg shadow">
                    <div class="flex items-center justify-between px-4 py-5 sm:px-6">
                        <h3 class="text-lg font-medium text-gray-900">List of Portfolios</h3>
                        <div class="flex gap-2">
                            <a href="{{ route('portfolio-m.create') }}"
                                class="inline-flex items-center px-4 py-2 font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 4v16m8-8H4" />
                                </svg>
                                Add New Portfolio
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filter Bar -->
                    <div class="px-4 py-4 border-t border-gray-200 bg-gray-50 sm:px-6">
                        <form method="GET">
                            <!-- Fixed section parameter -->
                            <input type="hidden" name="section" value="reits">

                            <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
                                <!-- Issuer Name Search Field -->
                                <div>
                                    <label for="search"
                                        class="block text-sm font-medium text-gray-700">Search</label>
                                    <input type="text" name="search" id="search"
                                        value="{{ old('search', request('search')) }}"
                                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        placeholder="Portfolio name..">
                                </div>

                                <!-- Status Filter -->
                                <div>
                                    <label for="status"
                                        class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" id="status"
                                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">All Status</option>
                                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>
                                            Draft</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>
                                            Active</option>
                                        <option value="pending"
                                            {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="rejected"
                                            {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        <option value="inactive"
                                            {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>

                                <!-- Filter Button -->
                                <div class="flex items-end">
                                    <button type="submit"
                                        class="inline-flex items-center px-4 py-2 font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                        </svg>
                                        Search
                                    </button>

                                    @if (request('search') || request('status'))
                                        <a href="{{ route('maker.dashboard', ['section' => 'reits']) }}"
                                            class="inline-flex items-center px-4 py-2 ml-2 font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                                            Clear
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="overflow-x-auto rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Name</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Trust Deed</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Annual Report</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Insurance</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Valuation Report</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Status</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                        Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse ($portfolios as $portfolio)
                                    <tr class="transition-colors hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            <a href="{{ route('property-m.index', $portfolio) }}"
                                                class="text-blue-600 cursor-pointer hover:text-blue-900">
                                                {{ $portfolio->portfolio_name }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            @if ($portfolio->trust_deed_document)
                                                <a href="{{ Storage::url($portfolio->trust_deed_document) }}"
                                                    class="text-indigo-600 hover:text-indigo-900">
                                                    Download
                                                </a>
                                            @else
                                                <span class="text-gray-500">N/A</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            @if ($portfolio->annual_report)
                                                <a href="{{ Storage::url($portfolio->annual_report) }}"
                                                    class="text-indigo-600 hover:text-indigo-900">
                                                    Download
                                                </a>
                                            @else
                                                <span class="text-gray-500">N/A</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            @if ($portfolio->insurance_document)
                                                <a href="{{ Storage::url($portfolio->insurance_document) }}"
                                                    class="text-indigo-600 hover:text-indigo-900">
                                                    Download
                                                </a>
                                            @else
                                                <span class="text-gray-500">N/A</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            @if ($portfolio->valuation_report)
                                                <a href="{{ Storage::url($portfolio->valuation_report) }}"
                                                    class="text-indigo-600 hover:text-indigo-900">
                                                    Download
                                                </a>
                                            @else
                                                <span class="text-gray-500">N/A</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-600 whitespace-nowrap">
                                            <span
                                                class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {{ match (strtolower($portfolio->status)) {
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'active' => 'bg-green-100 text-green-800',
                                            'inactive' => 'bg-gray-100 text-gray-800',
                                            'rejected' => 'bg-red-100 text-red-800',
                                            default => 'bg-gray-100 text-gray-800',
                                        } }}">
                                                {{ ucfirst($portfolio->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                                            <div class="flex justify-end space-x-2">
                                                @if ($portfolio->status == 'draft' or $portfolio->status == 'rejected')
                                                    <a href="{{ route('portfolio-m.approval', $portfolio) }}"
                                                        class="text-indigo-600 hover:text-indigo-900"
                                                        title="Submit for Approval"
                                                        onclick="confirmApproval(event, '{{ $portfolio->portfolio_name }}')">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M14 3v4a1 1 0 001 1h4" />
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M12 16v-5m0 0l-2 2m2-2l2 2" />
                                                        </svg>
                                                    </a>
                                                @endif
                                                <a href="{{ route('portfolio-m.show', $portfolio) }}"
                                                    class="text-indigo-600 hover:text-indigo-900" title="View">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                    </svg>
                                                </a>
                                                <a href="{{ route('portfolio-m.edit', $portfolio) }}"
                                                    class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                    </svg>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7"
                                            class="px-6 py-4 text-sm text-center text-gray-500 whitespace-nowrap">
                                            No portfolio found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- If the user has no permissions, show a message -->
    @if (
        !Auth::user()->hasPermission('DCMTRD') &&
            !Auth::user()->hasPermission('REITS') &&
            !Auth::user()->hasPermission('LEGAL') &&
            !Auth::user()->hasPermission('COMPLIANCE'))
        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="p-6 overflow-hidden bg-white shadow-xl sm:rounded-lg">
                    <div class="text-center">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('No Access') }}</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            {{ __('You do not have permission to access any modules.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- JavaScript for handling section display -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the section parameter from the URL
            const urlParams = new URLSearchParams(window.location.search);
            const section = urlParams.get('section');

            // Initially hide the default message (will show it if no valid section is found)
            const defaultMessage = document.getElementById('default-message');

            // Select all section elements
            const sections = document.querySelectorAll('.dashboard-section');

            // If a section parameter is present
            if (section) {
                // Find the target section
                const targetSection = document.querySelector(`[data-section="${section}"]`);

                if (targetSection) {
                    // Hide default message
                    if (defaultMessage) {
                        defaultMessage.classList.add('hidden');
                    }

                    // Show only the target section
                    targetSection.classList.remove('hidden');
                } else {
                    // If no valid section was found, show the default message
                    if (defaultMessage) {
                        defaultMessage.classList.remove('hidden');
                    }
                }
            } else {
                // If no section parameter, show the default message
                if (defaultMessage) {
                    defaultMessage.classList.remove('hidden');
                }
            }
        });
    </script>
</x-app-layout>
