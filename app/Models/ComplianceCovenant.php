<?php

namespace App\Models;

use App\Models\Issuer;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ComplianceCovenant extends Model implements Auditable
{
    use HasFactory, SoftDeletes, \OwenIt\Auditing\Auditable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'compliance_covenants';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'approval_datetime' => 'datetime',
        'letter_to_issuer' => 'date',
    ];

    /**
     * Get the issuer that this compliance covenant belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function issuer()
    {
        return $this->belongsTo(Issuer::class);
    }

    /**
     * Check if an issuer has completed all required documents
     *
     * @return bool
     */
    /**
     * Check if an issuer has completed all required documents
     *
     * @return bool
     */
    public function isCompliant(): bool
    {
        // Skip checks for fields marked as not required (Not Applicable)
        if ($this->afs_not_required) {
            $this->audited_financial_statements = null;
        }

        if ($this->cc_not_required) {
            $this->compliance_certificate = null;
        }

        if ($this->ufs_not_required) {
            $this->unaudited_financial_statements = null;
        }

        // Check if all document fields have values or are marked as not required
        return !empty($this->audited_financial_statements) ||
            $this->afs_not_required &&
            !empty($this->unaudited_financial_statements) ||
            $this->ufs_not_required &&
            !empty($this->compliance_certificate) ||
            $this->cc_not_required &&
            !empty($this->finance_service_cover_ratio) &&
            !empty($this->annual_budget) &&
            !empty($this->computation_of_finance_to_ebitda);
    }
    /**
     * Get all missing documents
     *
     * @return array
     */
    public function getMissingDocuments(): array
    {
        $missingDocuments = [];

        if (empty($this->audited_financial_statements)) {
            $missingDocuments[] = 'Audited Financial Statements';
        }

        if (empty($this->unaudited_financial_statements)) {
            $missingDocuments[] = 'Unaudited Financial Statements';
        }

        if (empty($this->compliance_certificate)) {
            $missingDocuments[] = 'Compliance Certificate';
        }

        if (empty($this->finance_service_cover_ratio)) {
            $missingDocuments[] = 'Finance Service Cover Ratio';
        }

        if (empty($this->annual_budget)) {
            $missingDocuments[] = 'Annual Budget';
        }

        if (empty($this->computation_of_finance_to_ebitda)) {
            $missingDocuments[] = 'Computation of Finance to EBITDA';
        }

        return $missingDocuments;
    }

    /**
     * Scope a query to find compliance records by issuer ID.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $issuerId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByIssuer(Builder $query, $issuerId): Builder
    {
        return $query->where('issuer_id', $issuerId);
    }

    /**
     * Scope a query to find records by financial year.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $year
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByFinancialYear(Builder $query, $year): Builder
    {
        return $query->where('financial_year_end', 'like', "%{$year}%");
    }

    /**
     * Scope a query to find fully compliant records.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompliant(Builder $query): Builder
    {
        return $query->whereNotNull('audited_financial_statements')
            ->whereNotNull('unaudited_financial_statements')
            ->whereNotNull('compliance_certificate')
            ->whereNotNull('finance_service_cover_ratio')
            ->whereNotNull('annual_budget')
            ->whereNotNull('computation_of_finance_to_ebitda');
    }

    /**
     * Scope a query to find non-compliant records.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNonCompliant(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('audited_financial_statements')
                ->orWhereNull('unaudited_financial_statements')
                ->orWhereNull('compliance_certificate')
                ->orWhereNull('finance_service_cover_ratio')
                ->orWhereNull('annual_budget')
                ->orWhereNull('computation_of_finance_to_ebitda');
        });
    }
}
