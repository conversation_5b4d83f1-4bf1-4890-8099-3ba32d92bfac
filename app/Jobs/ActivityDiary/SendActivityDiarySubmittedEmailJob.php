<?php

namespace App\Jobs\ActivityDiary;

use App\Models\User;
use App\Models\ActivityDiary;
use Illuminate\Support\Facades\Mail;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Mail\ActivityDiary\ActivityDiarySubmittedEmail;

class SendActivityDiarySubmittedEmailJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected ActivityDiary $activityDiary)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $emails = ['<EMAIL>', '<EMAIL>'];

        $users = User::whereIn('email', $emails)->get();

        foreach ($users as $user) {
            Mail::to($user->email)->send(new ActivityDiarySubmittedEmail($this->activityDiary, $user));
        }
    }
}
