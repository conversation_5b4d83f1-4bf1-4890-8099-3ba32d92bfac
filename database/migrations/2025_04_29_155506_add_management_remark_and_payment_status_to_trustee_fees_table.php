<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trustee_fees', function (Blueprint $table) {
            $table->dropColumn('date');
            $table->text('remark_to_management')->nullable()->after('remarks');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trustee_fees', function (Blueprint $table) {
            $table->integer('date')->nullable();
            $table->dropColumn(['remark_to_management']);
        });
    }
};
