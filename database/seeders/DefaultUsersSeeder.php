<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DefaultUsersSeeder extends Seeder
{
    public function run()
    {
        // Store current time to ensure consistency across all records
        $now = now();

        // First insert permissions
        $permissions = [
            [
                'name' => 'DCMTRD',
                'short_name' => 'DCMTRD',
                'full_name' => 'Debt Capital Market Trading',
                'description' => 'Access to debt capital market trading features',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'REITS',
                'short_name' => 'REITS',
                'full_name' => 'Real Estate Investment Trusts',
                'description' => 'Access to real estate investment trusts features',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'LEGAL',
                'short_name' => 'LEGAL',
                'full_name' => 'Legal Department',
                'description' => 'Access to legal department features',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'COMPLIANCE',
                'short_name' => 'COMPLIANCE',
                'full_name' => 'Compliance Department',
                'description' => 'Access to compliance features',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'SALES & MARKETING',
                'short_name' => 'SALES',
                'full_name' => 'Sales & Marketing Department',
                'description' => 'Access to sales & marketing features',
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('permissions')->insert($permissions);

        // Get the permission IDs
        $dcmtrdPermissionId = DB::table('permissions')->where('name', 'DCMTRD')->value('id');
        $reitsPermissionId = DB::table('permissions')->where('name', 'REITS')->value('id');
        $legalPermissionId = DB::table('permissions')->where('name', 'LEGAL')->value('id');
        $compliancePermissionId = DB::table('permissions')->where('name', 'COMPLIANCE')->value('id');
        $smdPermissionId = DB::table('permissions')->where('name', 'SALES & MARKETING')->value('id');

        // Sample data for users
        $usersData = [
            // Admin Users
            [
                'name' => 'MOHD ASHRAF BIN AZMI',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'job_title' => 'ASISSTANT MANAGER',
                'department' => 'DIGITALIZATION DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'MUQRI AMIN BIN MOHD SHAMSUDDIN',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'DIGITALIZATION DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NUR FARZANA BINTI ZAIRUL AZMI',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'job_title' => 'EXECUTIVE',
                'department' => 'DIGITALIZATION DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // DCMT Users
            [
                'name' => 'ROSLIM SYAH BIN IDRIS',
                'email' => '<EMAIL>',
                'role' => 'approver',
                'job_title' => 'SENIOR MANAGER',
                'department' => 'DEBT CAPITAL MARKET & TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'MOHAMAD AZAHARI BIN AB AZIZ',
                'email' => '<EMAIL>',
                'role' => 'approver',
                'job_title' => 'ASSISTANT MANAGER',
                'department' => 'DEBT CAPITAL MARKET & TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'FARAEDALISMALINA BINTI ZAKARIA',
                'email' => '<EMAIL>',
                'role' => 'maker',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'DEBT CAPITAL MARKET & TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NOR IZZAH BINTI MOHAMAD ARIFF',
                'email' => '<EMAIL>',
                'role' => 'maker',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'DEBT CAPITAL MARKET & TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NUR SAKIENAH BINTI KHAIRUDDIN',
                'email' => '<EMAIL>',
                'role' => 'maker',
                'job_title' => 'EXECUTIVE',
                'department' => 'DEBT CAPITAL MARKET & TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'MUHAMMAD SAYYIDI BIN MOHD BASIL',
                'email' => '<EMAIL>',
                'role' => 'maker',
                'job_title' => 'EXECUTIVE',
                'department' => 'DEBT CAPITAL MARKET & TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // REITS Users
            [
                'name' => 'DANG FATHIHAH BINTI IBRAHIM',
                'email' => '<EMAIL>',
                'role' => 'approver',
                'job_title' => 'ASSISTANT MANAGER',
                'department' => 'REAL ESTATE INVESTMENT TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NURUL SHAHIDAH BINTI RAS TAMAJIS',
                'email' => '<EMAIL>',
                'role' => 'maker',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'REAL ESTATE INVESTMENT TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'MUHAMMAD AFIS BIN AZMAN',
                'email' => '<EMAIL>',
                'role' => 'maker',
                'job_title' => 'EXECUTIVE',
                'department' => 'REAL ESTATE INVESTMENT TRUST UNIT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // Compliance
            [
                'name' => 'RASIDHA BINTI SAKHUDIN @ SALLEHUDIN',
                'email' => '<EMAIL>',
                'role' => 'compliance',
                'job_title' => 'SENIOR MANAGER',
                'department' => 'COMPLIANCE MONITORING DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'DALILA BINTI ZOBIR',
                'email' => '<EMAIL>',
                'role' => 'compliance',
                'job_title' => 'MANAGER',
                'department' => 'COMPLIANCE MONITORING DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'SHARIFAH NURAINI BINTI SYED KHALID',
                'email' => '<EMAIL>',
                'role' => 'compliance',
                'job_title' => 'MANAGER',
                'department' => 'COMPLIANCE MONITORING DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'DIAN NADIRAH BINTI JASRI',
                'email' => '<EMAIL>',
                'role' => 'compliance',
                'job_title' => 'ASSISTANT MANAGER',
                'department' => 'COMPLIANCE MONITORING DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NUR EFFA NAJIHAH BINTI ABDUL WAHAB',
                'email' => '<EMAIL>',
                'role' => 'compliance',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'COMPLIANCE MONITORING DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'KHAIRUN NISA BINTI ABD RAZAK',
                'email' => '<EMAIL>',
                'role' => 'compliance',
                'job_title' => 'EXECUTIVE',
                'department' => 'COMPLIANCE MONITORING DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NURUL SYAFIQAH BINTI ABD KADIR',
                'email' => '<EMAIL>',
                'role' => 'compliance',
                'job_title' => 'EXECUTIVE',
                'department' => 'COMPLIANCE MONITORING DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // Legal
            [
                'name' => 'ZULHIDA BINTI ABD MAURAD',
                'email' => '<EMAIL>',
                'role' => 'legal',
                'job_title' => 'SENIOR MANAGER',
                'department' => 'LEGAL DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NUR FARAH BINTI MOHD KAMAL',
                'email' => '<EMAIL>',
                'role' => 'legal',
                'job_title' => 'ASSISTANT MANAGER',
                'department' => 'LEGAL DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'ASMA NUR QURAISYAH BINTI YUNUS',
                'email' => '<EMAIL>',
                'role' => 'legal',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'LEGAL DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'AMIR AMSYAR BIN MOHD NAZIR',
                'email' => '<EMAIL>',
                'role' => 'legal',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'LEGAL DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'MAISARAH HUMAIRA BINTI MEOR YAHAYA',
                'email' => '<EMAIL>',
                'role' => 'legal',
                'job_title' => 'EXECUTIVE',
                'department' => 'LEGAL DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'KHARIESHA BINTI KHALID',
                'email' => '<EMAIL>',
                'role' => 'legal',
                'job_title' => 'EXECUTIVE',
                'department' => 'LEGAL DEPARTMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // SMD
            [
                'name' => 'FARAHIYAH BINTI HARUN',
                'email' => '<EMAIL>',
                'role' => 'sales',
                'job_title' => 'SENIOR MANAGER',
                'department' => 'MARKETING AND BUSINESS DEVELOPMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'SHAHRUL AZMAN BIN MOKHTAR',
                'email' => '<EMAIL>',
                'role' => 'sales',
                'job_title' => 'MANAGER',
                'department' => 'MARKETING AND BUSINESS DEVELOPMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'SHAMIM HUSAINI BIN SHAHRUL ANWAR',
                'email' => '<EMAIL>',
                'role' => 'sales',
                'job_title' => 'MANAGER',
                'department' => 'MARKETING AND BUSINESS DEVELOPMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'NURUL SHADILA BINTI ABDUL MALEK',
                'email' => '<EMAIL>',
                'role' => 'sales',
                'job_title' => 'SENIOR EXECUTIVE',
                'department' => 'MARKETING AND BUSINESS DEVELOPMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'FARAH HANANI BINTI JISNIZUMI',
                'email' => '<EMAIL>',
                'role' => 'sales',
                'job_title' => 'SEXECUTIVE',
                'department' => 'MARKETING AND BUSINESS DEVELOPMENT',
                'office_location' => 'AMANAHRAYA TRUSTEES BERHAD',
                'email_verified_at' => $now,
                'password' => bcrypt('Dcmtrd@2025'),
                'two_factor_code' => null,
                'two_factor_expires_at' => null,
                'two_factor_verified' => false,
                'two_factor_enabled' => false,
                'remember_token' => null,
                'last_login_at' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        // Insert data into the users table
        DB::table('users')->insert($usersData);

        // Now we need to create the permission_users relationships
        $permissionUserData = [];

        // Admin users get ALL permissions
        $adminEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($adminEmails as $email) {
            $userId = DB::table('users')->where('email', $email)->value('id');

            // Give admin users all permissions
            $permissionUserData[] = [
                'user_id' => $userId,
                'permission_id' => $dcmtrdPermissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $permissionUserData[] = [
                'user_id' => $userId,
                'permission_id' => $reitsPermissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $permissionUserData[] = [
                'user_id' => $userId,
                'permission_id' => $legalPermissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $permissionUserData[] = [
                'user_id' => $userId,
                'permission_id' => $compliancePermissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $permissionUserData[] = [
                'user_id' => $userId,
                'permission_id' => $smdPermissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // DCMTRD Users
        $dcmtrdEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($dcmtrdEmails as $email) {
            $userId = DB::table('users')->where('email', $email)->value('id');
            if ($userId) {
                $permissionUserData[] = [
                    'user_id' => $userId,
                    'permission_id' => $dcmtrdPermissionId,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        // Special case: ROSLIM SYAH BIN IDRIS has both DCMTRD and REITS
        $roslimId = DB::table('users')->where('email', '<EMAIL>')->value('id');
        if ($roslimId) {
            $permissionUserData[] = [
                'user_id' => $roslimId,
                'permission_id' => $reitsPermissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // REITS Users
        $reitsEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($reitsEmails as $email) {
            $userId = DB::table('users')->where('email', $email)->value('id');
            if ($userId) {
                $permissionUserData[] = [
                    'user_id' => $userId,
                    'permission_id' => $reitsPermissionId,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        // Compliance Officers get COMPLIANCE, DCMTRD and REITS permissions
        $complianceEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($complianceEmails as $email) {
            $userId = DB::table('users')->where('email', $email)->value('id');
            // Add COMPLIANCE permission
            $permissionUserData[] = [
                'user_id' => $userId,
                'permission_id' => $compliancePermissionId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Legal users get LEGAL, DCMTRD and REITS permissions
        $legalEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($legalEmails as $email) {
            $userId = DB::table('users')->where('email', $email)->value('id');
            if ($userId) {
                // Add LEGAL permission
                $permissionUserData[] = [
                    'user_id' => $userId,
                    'permission_id' => $legalPermissionId,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        // Sales & Marketing users get SALES_MARKETING
        $salesMarketingEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        foreach ($salesMarketingEmails as $email) {
            $userId = DB::table('users')->where('email', $email)->value('id');
            if ($userId) {
                // Add SALES_MARKETING permission
                $permissionUserData[] = [
                    'user_id' => $userId,
                    'permission_id' => $smdPermissionId,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        // Insert the permission relationships
        DB::table('permission_users')->insert($permissionUserData);
    }
}
