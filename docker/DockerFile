FROM php:8.4.1-fpm

ARG NODE_VERSION=22

LABEL maintainer="DCMTRD APP"

ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin

RUN chmod +x /usr/local/bin/install-php-extensions \
    && install-php-extensions @composer \
    gd \
    curl \
    pdo \
    pdo_mysql \
    mbstring \
    xml \
    zip \
    bcmath \
    intl \
    readline \
    msgpack \
    igbinary \
    redis \
    pcntl

RUN apt-get update \
    && apt-get install -y ca-certificates curl gnupg \
    && mkdir -p /etc/apt/keyrings \
    && curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_VERSION.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list \
    && apt-get update \
    && apt-get install -y nodejs \
    && npm install -g npm \
    && apt-get install -y cron supervisor \
    && crontab -l | { cat; echo "* * * * * cd /var/www/html && /usr/local/bin/php artisan schedule:run >> /dev/null 2>&1"; } | crontab - 

COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf        

CMD /usr/bin/supervisord